# Card Payment Binary Converter

This project implements a converter that transforms card payment requests from JSON format into a proprietary binary format used by downstream systems.

## Overview

The converter was reverse-engineered from sample input/output data:
- **Input**: JSON payment request (`src/main/resources/request_sample.json`)
- **Output**: Hex-encoded binary format (`src/main/resources/output_sample.txt`)

## Binary Format Structure

The binary format follows this structure:

1. **Card Number Length** (1 byte): `0x10` (16 decimal)
2. **Card Number** (8 bytes): Each byte contains 2 digits in BCD format
3. **Timestamp & Expiry** (8 bytes): `YYMMDDHHMMSSMMYY` in BCD format
4. **Reserved/Padding** (8 bytes): `00 00 00 00 10 00 24 01`
5. **CVC** (variable): Length byte + ASCII data
6. **Field Separator** (2 bytes): `02 02`
7. **Merchant Name** (variable): Length byte + ASCII data (max 11 chars)
8. **Address Line 2** (variable): `03` + length byte + ASCII data
9. **Postcode** (variable): `04` + length byte + ASCII data (spaces removed)
10. **Checksum** (2 bytes): `08` + calculated checksum

### Key Encoding Details

- **Card Number**: Stored as BCD (Binary Coded Decimal) - each digit pair becomes one byte
- **Timestamps**: BCD format where `25` becomes `0x25` (not `0x19`)
- **Strings**: Length-prefixed ASCII
- **Merchant Name**: Truncated to 11 characters maximum
- **Postcode**: Spaces are removed before encoding
- **Checksum**: Sum of all bytes XOR 0x14

## Usage

### Running the Converter

```bash
./gradlew run
```

### Running Tests

```bash
./gradlew test
```

### Using the API

```java
BinaryConverter converter = new BinaryConverter();
String jsonInput = "{ ... }"; // Your JSON payment request
String hexOutput = converter.convertToBinary(jsonInput);
```

## Project Structure

```
src/
├── main/
│   ├── java/
│   │   └── org/example/
│   │       ├── BinaryConverter.java      # Main converter class
│   │       ├── Main.java                 # Demo application
│   │       └── model/
│   │           └── PaymentRequest.java   # JSON data model
│   └── resources/
│       ├── request_sample.json           # Sample input
│       └── output_sample.txt             # Expected output
└── test/
    └── java/
        └── org/example/
            └── BinaryConverterTest.java  # Unit tests
```

## Dependencies

- Jackson (JSON processing)
- JUnit 5 (testing)

## Sample Data

**Input JSON:**
```json
{
  "timestamp": "2025-05-31T11:55:39.000Z",
  "amount": {
    "value": 1000,
    "currency": "GBP"
  },
  "card": {
    "number": "****************",
    "expiry_month": 12,
    "expiry_year": 2025,
    "cvc": "123"
  },
  "merchant": {
    "name": "Merchant Name",
    "address": {
      "line1": "Merchant Street",
      "line2": "London",
      "postcode": "SW18 4GG"
    }
  }
}
```

**Output (hex):**
```
10****************250531115539122500000000100024010331323302020B4D65726368616E74204E6103064C6F6E646F6E0407535731383447470826
```

## Testing

The implementation includes comprehensive unit tests that verify the converter produces the exact expected binary output for the sample data. The test performs byte-by-byte comparison to ensure accuracy.

## Future Enhancements

To extend this converter for additional samples:

1. Add more test cases with different input/output pairs
2. Identify any variations in the binary format
3. Refine the encoding rules based on additional data points
4. Add validation for input data constraints
