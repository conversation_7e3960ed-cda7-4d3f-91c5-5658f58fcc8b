package org.example;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class BinaryConverterTest {
    private BinaryConverter converter;
    private String sampleJson;
    private String expectedOutput;

    @BeforeEach
    void setUp() throws IOException {
        converter = new BinaryConverter();

        // Load the sample files
        sampleJson = Files.readString(Paths.get("src/main/resources/request_sample.json"));
        expectedOutput = Files.readString(Paths.get("src/main/resources/output_sample.txt")).trim();
    }

    @Test
    void testConvertSampleData() throws IOException {
        String actualOutput = converter.convertToBinary(sampleJson);

        System.out.println("Expected: " + expectedOutput);
        System.out.println("Actual:   " + actualOutput);
        System.out.println("Length expected: " + expectedOutput.length());
        System.out.println("Length actual:   " + actualOutput.length());

        // For now, let's just check that we get some output and analyze the differences
        assertNotNull(actualOutput);
        assertFalse(actualOutput.isEmpty());

        // Let's compare byte by byte to understand the differences
        if (!expectedOutput.equals(actualOutput)) {
            System.out.println("\nByte-by-byte comparison:");
            int minLength = Math.min(expectedOutput.length(), actualOutput.length());

            for (int i = 0; i < minLength; i += 2) {
                String expectedByte = expectedOutput.substring(i, Math.min(i + 2, expectedOutput.length()));
                String actualByte = actualOutput.substring(i, Math.min(i + 2, actualOutput.length()));

                if (!expectedByte.equals(actualByte)) {
                    System.out.printf("Pos %2d: Expected %s, Got %s%n", i/2, expectedByte, actualByte);
                }
            }

            if (expectedOutput.length() != actualOutput.length()) {
                System.out.println("Length mismatch - expected: " + expectedOutput.length() + ", actual: " + actualOutput.length());
            }
        }

        // Now that we've fixed the issues, let's assert equality
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    void testConvertEmptyJson() {
        assertThrows(Exception.class, () -> {
            converter.convertToBinary("{}");
        });
    }
}
