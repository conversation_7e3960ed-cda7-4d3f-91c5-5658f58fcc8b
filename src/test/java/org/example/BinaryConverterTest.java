package org.example;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class BinaryConverterTest {
    private BinaryConverter converter;
    private String sampleJson;
    private String expectedOutput;

    @BeforeEach
    void setUp() throws IOException {
        converter = new BinaryConverter();

        // Load the sample files
        sampleJson = Files.readString(Paths.get("src/main/resources/request_sample.json"));
        expectedOutput = Files.readString(Paths.get("src/main/resources/output_sample.txt")).trim();
    }

    @Test
    void testConvertSampleData() throws IOException {
        String actualOutput = converter.convertToBinary(sampleJson);

        System.out.println("Expected: " + expectedOutput);
        System.out.println("Actual:   " + actualOutput);
        System.out.println("Length expected: " + expectedOutput.length());
        System.out.println("Length actual:   " + actualOutput.length());

        // For now, let's just check that we get some output and analyze the differences
        assertNotNull(actualOutput);
        assertFalse(actualOutput.isEmpty());

        // Let's compare byte by byte to understand the differences
        if (!expectedOutput.equals(actualOutput)) {
            System.out.println("\nByte-by-byte comparison:");
            int minLength = Math.min(expectedOutput.length(), actualOutput.length());

            for (int i = 0; i < minLength; i += 2) {
                String expectedByte = expectedOutput.substring(i, Math.min(i + 2, expectedOutput.length()));
                String actualByte = actualOutput.substring(i, Math.min(i + 2, actualOutput.length()));

                if (!expectedByte.equals(actualByte)) {
                    System.out.printf("Pos %2d: Expected %s, Got %s%n", i/2, expectedByte, actualByte);
                }
            }

            if (expectedOutput.length() != actualOutput.length()) {
                System.out.println("Length mismatch - expected: " + expectedOutput.length() + ", actual: " + actualOutput.length());
            }
        }

        // Now that we've fixed the issues, let's assert equality
        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    void testConvertSample2Data() throws IOException {
        // Load the second sample files
        String sampleJson2 = Files.readString(Paths.get("src/main/resources/request_sample2.json"));
        String expectedOutput2 = Files.readString(Paths.get("src/main/resources/output_sample2.txt")).trim();

        String actualOutput2 = converter.convertToBinary(sampleJson2);

        System.out.println("=== SAMPLE 2 TEST ===");
        System.out.println("Expected: " + expectedOutput2);
        System.out.println("Actual:   " + actualOutput2);
        System.out.println("Length expected: " + expectedOutput2.length());
        System.out.println("Length actual:   " + actualOutput2.length());

        // Compare byte by byte if different
        if (!expectedOutput2.equals(actualOutput2)) {
            System.out.println("\nByte-by-byte comparison:");
            int minLength = Math.min(expectedOutput2.length(), actualOutput2.length());

            for (int i = 0; i < minLength; i += 2) {
                String expectedByte = expectedOutput2.substring(i, Math.min(i + 2, expectedOutput2.length()));
                String actualByte = actualOutput2.substring(i, Math.min(i + 2, actualOutput2.length()));

                if (!expectedByte.equals(actualByte)) {
                    System.out.printf("Pos %2d: Expected %s, Got %s%n", i/2, expectedByte, actualByte);
                }
            }

            if (expectedOutput2.length() != actualOutput2.length()) {
                System.out.println("Length mismatch - expected: " + expectedOutput2.length() + ", actual: " + actualOutput2.length());
            }
        }

        assertEquals(expectedOutput2, actualOutput2);
    }

    @Test
    void testBothSamplesWork() throws IOException {
        // Verify both samples work correctly
        String sample1Json = Files.readString(Paths.get("src/main/resources/request_sample.json"));
        String expected1 = Files.readString(Paths.get("src/main/resources/output_sample.txt")).trim();
        String actual1 = converter.convertToBinary(sample1Json);

        String sample2Json = Files.readString(Paths.get("src/main/resources/request_sample2.json"));
        String expected2 = Files.readString(Paths.get("src/main/resources/output_sample2.txt")).trim();
        String actual2 = converter.convertToBinary(sample2Json);

        assertEquals(expected1, actual1, "Sample 1 should match");
        assertEquals(expected2, actual2, "Sample 2 should match");

        System.out.println("✅ Both samples converted correctly!");
        System.out.println("Sample 1 (16-digit): " + (actual1.equals(expected1) ? "PASS" : "FAIL"));
        System.out.println("Sample 2 (5-digit): " + (actual2.equals(expected2) ? "PASS" : "FAIL"));
    }

    @Test
    void testConvertEmptyJson() {
        assertThrows(Exception.class, () -> {
            converter.convertToBinary("{}");
        });
    }
}
