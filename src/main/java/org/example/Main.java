package org.example;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class Main {
    public static void main(String[] args) {
        try {
            BinaryConverter converter = new BinaryConverter();

            System.out.println("=== Card Payment Binary Converter ===");

            // Test Sample 1
            System.out.println("\n--- Sample 1 (16-digit card) ---");
            testSample(converter, "src/main/resources/request_sample.json",
                      "src/main/resources/output_sample.txt");

            // Test Sample 2
            System.out.println("\n--- Sample 2 (5-digit card, USD, CVC) ---");
            testSample(converter, "src/main/resources/request_sample2.json",
                      "src/main/resources/output_sample2.txt");

            // Test Sample 3
            System.out.println("\n--- Sample 3 (5-digit card, EUR, no CVC) ---");
            testSample(converter, "src/main/resources/request_sample3.json",
                      "src/main/resources/output_sample3.txt");

        } catch (IOException e) {
            System.err.println("Error: " + e.getMessage());
        }
    }

    private static void testSample(BinaryConverter converter, String inputFile, String outputFile)
            throws IOException {
        String jsonInput = Files.readString(Paths.get(inputFile));
        String binaryOutput = converter.convertToBinary(jsonInput);
        String expectedOutput = Files.readString(Paths.get(outputFile)).trim();

        System.out.println("Input: " + inputFile);
        System.out.println("Output: " + binaryOutput);
        System.out.println("Expected: " + expectedOutput);
        System.out.println("Match: " + binaryOutput.equals(expectedOutput));
    }
}