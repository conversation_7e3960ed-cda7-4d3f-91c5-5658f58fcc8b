package org.example;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class Main {
    public static void main(String[] args) {
        try {
            BinaryConverter converter = new BinaryConverter();

            // Load and convert the sample JSON
            String jsonInput = Files.readString(Paths.get("src/main/resources/request_sample.json"));
            String binaryOutput = converter.convertToBinary(jsonInput);

            System.out.println("=== Card Payment Binary Converter ===");
            System.out.println("Input JSON:");
            System.out.println(jsonInput);
            System.out.println("\nBinary Output (hex):");
            System.out.println(binaryOutput);

            // Load expected output for comparison
            String expectedOutput = Files.readString(Paths.get("src/main/resources/output_sample.txt")).trim();
            System.out.println("\nExpected Output:");
            System.out.println(expectedOutput);

            System.out.println("\nMatch: " + binaryOutput.equals(expectedOutput));

        } catch (IOException e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
}